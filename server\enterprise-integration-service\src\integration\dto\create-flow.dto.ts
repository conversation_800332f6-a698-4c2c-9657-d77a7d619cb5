import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsArray,
  IsObject,
  ValidateNested,
  Min,
  Max,
  Length,
  IsUUID,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  SyncMode,
  ConflictResolution,
  TransformationType,
} from '../entities/integration-flow.entity';

/**
 * 数据映射规则DTO
 */
export class DataMappingDto {
  @ApiProperty({
    description: '源字段名',
    example: 'customer_name',
  })
  @IsString()
  @Length(1, 100)
  sourceField: string;

  @ApiProperty({
    description: '目标字段名',
    example: 'customerName',
  })
  @IsString()
  @Length(1, 100)
  targetField: string;

  @ApiPropertyOptional({
    description: '转换表达式',
    example: 'UPPER(${value})',
  })
  @IsOptional()
  @IsString()
  transformation?: string;

  @ApiPropertyOptional({
    description: '验证规则',
    example: 'LENGTH(${value}) > 0',
  })
  @IsOptional()
  @IsString()
  validation?: string;

  @ApiProperty({
    description: '是否必填',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  @ApiPropertyOptional({
    description: '默认值',
    example: 'Unknown',
  })
  @IsOptional()
  defaultValue?: any;
}

/**
 * 转换步骤DTO
 */
export class TransformationStepDto {
  @ApiProperty({
    description: '步骤ID',
    example: 'step-001',
  })
  @IsString()
  @Length(1, 50)
  stepId: string;

  @ApiProperty({
    description: '转换类型',
    enum: TransformationType,
    example: TransformationType.MAPPING,
  })
  @IsEnum(TransformationType)
  type: TransformationType;

  @ApiProperty({
    description: '配置参数',
    example: { rules: ['rule1', 'rule2'] },
  })
  @IsObject()
  configuration: any;

  @ApiProperty({
    description: '执行顺序',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  order: number;
}

/**
 * 错误处理配置DTO
 */
export class ErrorHandlingDto {
  @ApiPropertyOptional({
    description: '最大重试次数',
    example: 3,
    default: 3,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  maxRetries?: number;

  @ApiPropertyOptional({
    description: '退避策略',
    example: 'exponential',
    default: 'exponential',
  })
  @IsOptional()
  @IsString()
  @Matches(/^(linear|exponential|fixed)$/)
  backoffStrategy?: string;

  @ApiPropertyOptional({
    description: '基础延迟时间（毫秒）',
    example: 1000,
    default: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(60000)
  baseDelay?: number;

  @ApiPropertyOptional({
    description: '是否启用死信队列',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  deadLetterQueue?: boolean;
}

/**
 * 告警配置DTO
 */
export class AlertingDto {
  @ApiPropertyOptional({
    description: '是否启用告警',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({
    description: '告警渠道',
    example: ['email', 'slack', 'webhook'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  channels?: string[];

  @ApiPropertyOptional({
    description: '告警阈值',
    example: 5,
    default: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  threshold?: number;
}

/**
 * 创建集成流程DTO
 */
export class CreateFlowDto {
  @ApiProperty({
    description: '流程名称',
    example: 'ERP到CRM客户数据同步',
  })
  @IsString()
  @Length(1, 200)
  name: string;

  @ApiPropertyOptional({
    description: '流程描述',
    example: '将ERP系统中的客户数据实时同步到CRM系统',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '源系统ID',
    example: 'erp-system-001',
  })
  @IsString()
  sourceSystem: string;

  @ApiProperty({
    description: '目标系统ID',
    example: 'crm-system-001',
  })
  @IsString()
  targetSystem: string;

  @ApiProperty({
    description: '数据实体列表',
    example: ['customers', 'orders'],
  })
  @IsArray()
  @IsString({ each: true })
  dataEntities: string[];

  @ApiPropertyOptional({
    description: '同步模式',
    enum: SyncMode,
    example: SyncMode.REAL_TIME,
    default: SyncMode.BATCH,
  })
  @IsOptional()
  @IsEnum(SyncMode)
  syncMode?: SyncMode;

  @ApiPropertyOptional({
    description: '调度表达式（Cron格式）',
    example: '0 */1 * * * *',
  })
  @IsOptional()
  @IsString()
  @Matches(/^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/)
  schedule?: string;

  @ApiPropertyOptional({
    description: '批处理大小',
    example: 1000,
    default: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10000)
  batchSize?: number;

  @ApiPropertyOptional({
    description: '冲突解决策略',
    enum: ConflictResolution,
    example: ConflictResolution.SOURCE_WINS,
    default: ConflictResolution.SOURCE_WINS,
  })
  @IsOptional()
  @IsEnum(ConflictResolution)
  conflictResolution?: ConflictResolution;

  @ApiPropertyOptional({
    description: '是否启用增量同步',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  deltaSync?: boolean;

  @ApiPropertyOptional({
    description: '是否启用压缩',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  compressionEnabled?: boolean;

  @ApiPropertyOptional({
    description: '数据映射规则',
    type: [DataMappingDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DataMappingDto)
  dataMappings?: DataMappingDto[];

  @ApiPropertyOptional({
    description: '数据转换步骤',
    type: [TransformationStepDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TransformationStepDto)
  transformationSteps?: TransformationStepDto[];

  @ApiPropertyOptional({
    description: '数据过滤条件',
    example: { status: 'active', created_date: { $gte: '2023-01-01' } },
  })
  @IsOptional()
  @IsObject()
  filterConditions?: any;

  @ApiPropertyOptional({
    description: '错误处理配置',
    type: ErrorHandlingDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ErrorHandlingDto)
  errorHandling?: ErrorHandlingDto;

  @ApiPropertyOptional({
    description: '告警配置',
    type: AlertingDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AlertingDto)
  alerting?: AlertingDto;
}
