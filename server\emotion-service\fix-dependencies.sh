#!/bin/bash

# 情感分析服务依赖修复脚本

echo "🔧 修复情感分析服务依赖问题..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
    echo "✅ Node.js 版本符合要求: $NODE_VERSION"
else
    echo "❌ Node.js 版本过低: $NODE_VERSION，需要 >= $REQUIRED_VERSION"
    echo "请升级 Node.js 版本"
    exit 1
fi

# 清理旧的依赖
echo "🧹 清理旧的依赖..."
if [ -d "node_modules" ]; then
    echo "删除 node_modules 目录..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "删除 package-lock.json 文件..."
    rm package-lock.json
fi

# 清理 npm 缓存
echo "🗑️  清理 npm 缓存..."
npm cache clean --force

# 安装依赖
echo "📦 安装项目依赖..."
npm install

# 检查安装结果
if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功！"
    
    # 验证关键依赖
    echo "🔍 验证关键依赖..."
    
    if npm list @node-rs/jieba > /dev/null 2>&1; then
        echo "✅ @node-rs/jieba 安装成功"
    else
        echo "❌ @node-rs/jieba 安装失败"
    fi
    
    if npm list ioredis > /dev/null 2>&1; then
        echo "✅ ioredis 安装成功"
    else
        echo "❌ ioredis 安装失败"
    fi
    
    if npm list @nestjs/core > /dev/null 2>&1; then
        echo "✅ @nestjs/core 安装成功"
    else
        echo "❌ @nestjs/core 安装失败"
    fi
    
    echo ""
    echo "🎉 依赖修复完成！"
    echo ""
    echo "📝 下一步操作："
    echo "1. 配置环境变量: cp .env.example .env"
    echo "2. 编辑 .env 文件配置数据库连接"
    echo "3. 启动服务: npm run start:dev"
    echo ""
    
else
    echo "❌ 依赖安装失败！"
    echo ""
    echo "🔍 可能的解决方案："
    echo "1. 检查网络连接"
    echo "2. 尝试使用不同的 npm 镜像源："
    echo "   npm config set registry https://registry.npmmirror.com/"
    echo "3. 如果在中国，可以使用淘宝镜像："
    echo "   npm install -g cnpm --registry=https://registry.npmmirror.com"
    echo "   cnpm install"
    echo "4. 检查是否有权限问题"
    echo ""
    exit 1
fi
