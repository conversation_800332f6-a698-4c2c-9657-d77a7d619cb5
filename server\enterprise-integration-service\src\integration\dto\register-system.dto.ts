import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsUrl,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsArray,
  IsObject,
  ValidateNested,
  Min,
  Max,
  Length,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  EnterpriseSystemType,
  IntegrationProtocol,
  AuthenticationType,
} from '../entities/enterprise-system.entity';

/**
 * 认证配置DTO
 */
export class AuthenticationConfigDto {
  @ApiProperty({
    description: '认证类型',
    enum: AuthenticationType,
    example: AuthenticationType.API_KEY,
  })
  @IsEnum(AuthenticationType)
  type: AuthenticationType;

  @ApiProperty({
    description: '认证凭据',
    example: { apiKey: 'your-api-key' },
  })
  @IsObject()
  credentials: any;

  @ApiPropertyOptional({
    description: '令牌端点',
    example: 'https://api.example.com/oauth/token',
  })
  @IsOptional()
  @IsUrl()
  tokenEndpoint?: string;
}

/**
 * 系统能力配置DTO
 */
export class SystemCapabilitiesDto {
  @ApiProperty({
    description: '支持的操作',
    example: ['create', 'read', 'update', 'delete'],
  })
  @IsArray()
  @IsString({ each: true })
  supportedOperations: string[];

  @ApiProperty({
    description: '支持的数据格式',
    example: ['json', 'xml', 'csv'],
  })
  @IsArray()
  @IsString({ each: true })
  dataFormats: string[];

  @ApiProperty({
    description: '单次请求最大记录数',
    example: 1000,
  })
  @IsNumber()
  @Min(1)
  @Max(10000)
  maxRecordsPerRequest: number;

  @ApiProperty({
    description: '每秒请求限制',
    example: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(1000)
  requestsPerSecond: number;

  @ApiProperty({
    description: '每小时请求限制',
    example: 1000,
  })
  @IsNumber()
  @Min(1)
  @Max(100000)
  requestsPerHour: number;

  @ApiProperty({
    description: '是否支持批量操作',
    example: true,
  })
  @IsBoolean()
  bulkOperations: boolean;

  @ApiProperty({
    description: '是否支持Webhook',
    example: false,
  })
  @IsBoolean()
  webhookSupport: boolean;
}

/**
 * 注册企业系统DTO
 */
export class RegisterSystemDto {
  @ApiPropertyOptional({
    description: '系统唯一标识（可选，系统自动生成）',
    example: 'erp-system-001',
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  systemId?: string;

  @ApiProperty({
    description: '系统名称',
    example: 'SAP ERP系统',
  })
  @IsString()
  @Length(1, 200)
  name: string;

  @ApiProperty({
    description: '系统类型',
    enum: EnterpriseSystemType,
    example: EnterpriseSystemType.ERP,
  })
  @IsEnum(EnterpriseSystemType)
  type: EnterpriseSystemType;

  @ApiProperty({
    description: '系统厂商',
    example: 'SAP',
  })
  @IsString()
  @Length(1, 100)
  vendor: string;

  @ApiProperty({
    description: '系统版本',
    example: 'S/4HANA 2021',
  })
  @IsString()
  @Length(1, 50)
  version: string;

  @ApiProperty({
    description: '系统端点地址',
    example: 'https://erp.company.com/api/v1',
  })
  @IsUrl()
  endpoint: string;

  @ApiProperty({
    description: '集成协议',
    enum: IntegrationProtocol,
    example: IntegrationProtocol.REST_API,
  })
  @IsEnum(IntegrationProtocol)
  protocol: IntegrationProtocol;

  @ApiProperty({
    description: '认证配置',
    type: AuthenticationConfigDto,
  })
  @ValidateNested()
  @Type(() => AuthenticationConfigDto)
  authentication: AuthenticationConfigDto;

  @ApiPropertyOptional({
    description: '系统描述',
    example: '公司主要的ERP系统，管理财务、采购、销售等业务',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: '最大连接数',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxConnections?: number;

  @ApiPropertyOptional({
    description: '连接超时时间（毫秒）',
    example: 30000,
    default: 30000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000)
  connectionTimeout?: number;

  @ApiPropertyOptional({
    description: '重试次数',
    example: 3,
    default: 3,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  retryAttempts?: number;

  @ApiPropertyOptional({
    description: '系统能力配置',
    type: SystemCapabilitiesDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SystemCapabilitiesDto)
  capabilities?: SystemCapabilitiesDto;

  @ApiPropertyOptional({
    description: '是否启用加密',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  encryptionEnabled?: boolean;

  @ApiPropertyOptional({
    description: '加密算法',
    example: 'AES-256',
    default: 'AES-256',
  })
  @IsOptional()
  @IsString()
  encryptionAlgorithm?: string;

  @ApiPropertyOptional({
    description: 'PII字段列表',
    example: ['email', 'phone', 'ssn'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  piiFields?: string[];

  @ApiPropertyOptional({
    description: '是否启用数据匿名化',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  anonymization?: boolean;

  @ApiPropertyOptional({
    description: '数据保留天数',
    example: 365,
    default: 365,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(3650)
  dataRetentionDays?: number;

  @ApiPropertyOptional({
    description: '是否启用审计日志',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  auditLogging?: boolean;
}
