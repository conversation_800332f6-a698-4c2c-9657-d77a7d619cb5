{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"webpack": false, "tsConfigPath": "tsconfig.json"}, "projects": {"enterprise-integration-service": {"type": "application", "root": "", "entryFile": "main", "sourceRoot": "src", "compilerOptions": {"tsConfigPath": "tsconfig.json"}}}, "monorepo": false, "root": "", "entryFile": "main"}