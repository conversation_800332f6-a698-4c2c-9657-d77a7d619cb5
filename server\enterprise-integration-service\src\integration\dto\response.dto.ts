import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 基础API响应DTO
 */
export class ApiResponseDto {
  @ApiProperty({
    description: '请求是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '响应消息',
    example: '操作成功',
  })
  message: string;

  @ApiPropertyOptional({
    description: '响应数据',
    example: { id: '12345' },
  })
  data?: any;

  @ApiProperty({
    description: '响应时间戳',
    example: '2023-12-01T10:00:00.000Z',
  })
  timestamp: Date;
}

/**
 * 系统状态响应DTO
 */
export class SystemStatusResponseDto extends ApiResponseDto {
  @ApiProperty({
    description: '系统状态数据',
    example: {
      system: {
        id: 'erp-system-001',
        name: 'SAP ERP系统',
        type: 'erp',
        status: 'active',
        lastSync: '2023-12-01T09:30:00.000Z'
      },
      connection: {
        status: 'connected',
        responseTime: 150,
        lastCheck: '2023-12-01T10:00:00.000Z'
      },
      performance: {
        averageResponseTime: 200,
        requestsPerSecond: 15,
        errorRate: 0.02
      },
      recentTasks: [
        {
          taskId: 'task-001',
          status: 'completed',
          recordsProcessed: 1000
        }
      ]
    },
  })
  data: {
    system: {
      id: string;
      name: string;
      type: string;
      status: string;
      lastSync: Date;
    };
    connection: {
      status: string;
      responseTime: number;
      lastCheck: Date;
    };
    performance: {
      averageResponseTime: number;
      requestsPerSecond: number;
      errorRate: number;
    };
    recentTasks: Array<{
      taskId: string;
      status: string;
      recordsProcessed: number;
    }>;
  };
}

/**
 * 集成统计响应DTO
 */
export class IntegrationStatsResponseDto extends ApiResponseDto {
  @ApiProperty({
    description: '集成统计数据',
    example: {
      overview: {
        totalSystems: 5,
        activeSystems: 4,
        totalFlows: 10,
        activeFlows: 8,
        totalSyncTasks: 100,
        successfulSyncs: 95,
        failedSyncs: 5,
        averageSyncTime: 30000,
        dataVolume: 1000000
      },
      systems: [
        {
          systemId: 'erp-system-001',
          name: 'SAP ERP系统',
          type: 'erp',
          status: 'active',
          lastSync: '2023-12-01T09:30:00.000Z'
        }
      ],
      flows: [
        {
          flowId: 'flow-001',
          name: 'ERP到CRM同步',
          status: 'active',
          statistics: {
            totalExecutions: 50,
            successfulExecutions: 48,
            failedExecutions: 2
          }
        }
      ],
      activeTasks: [
        {
          taskId: 'task-001',
          flowId: 'flow-001',
          status: 'running',
          progress: 75,
          recordsProcessed: 750
        }
      ]
    },
  })
  data: {
    overview: {
      totalSystems: number;
      activeSystems: number;
      totalFlows: number;
      activeFlows: number;
      totalSyncTasks: number;
      successfulSyncs: number;
      failedSyncs: number;
      averageSyncTime: number;
      dataVolume: number;
    };
    systems: Array<{
      systemId: string;
      name: string;
      type: string;
      status: string;
      lastSync: Date;
    }>;
    flows: Array<{
      flowId: string;
      name: string;
      status: string;
      statistics: {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
      };
    }>;
    activeTasks: Array<{
      taskId: string;
      flowId: string;
      status: string;
      progress: number;
      recordsProcessed: number;
    }>;
  };
}

/**
 * 同步任务响应DTO
 */
export class SyncTaskResponseDto extends ApiResponseDto {
  @ApiProperty({
    description: '同步任务数据',
    example: {
      taskId: 'task-001',
      flowId: 'flow-001',
      status: 'running',
      startTime: '2023-12-01T10:00:00.000Z',
      progress: 0,
      recordsToProcess: 1000,
      recordsProcessed: 0
    },
  })
  data: {
    taskId: string;
    flowId?: string;
    status: string;
    startTime: Date;
    progress: number;
    recordsToProcess: number;
    recordsProcessed: number;
  };
}
