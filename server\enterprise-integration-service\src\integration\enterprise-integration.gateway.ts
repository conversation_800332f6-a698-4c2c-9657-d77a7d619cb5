import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { EnterpriseIntegrationService } from './enterprise-integration.service';

/**
 * 企业集成WebSocket网关
 * 提供实时数据同步状态推送
 */
@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: '/enterprise-integration',
})
export class EnterpriseIntegrationGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(EnterpriseIntegrationGateway.name);
  private connectedClients = new Map<string, Socket>();

  constructor(
    private readonly enterpriseIntegrationService: EnterpriseIntegrationService,
  ) {}

  /**
   * 网关初始化
   */
  afterInit(server: Server) {
    this.logger.log('企业集成WebSocket网关已初始化');
  }

  /**
   * 客户端连接
   */
  handleConnection(client: Socket) {
    this.logger.log(`客户端连接: ${client.id}`);
    this.connectedClients.set(client.id, client);
    
    // 发送欢迎消息
    client.emit('connected', {
      message: '已连接到企业集成服务',
      clientId: client.id,
      timestamp: new Date(),
    });
  }

  /**
   * 客户端断开连接
   */
  handleDisconnect(client: Socket) {
    this.logger.log(`客户端断开连接: ${client.id}`);
    this.connectedClients.delete(client.id);
  }

  /**
   * 订阅系统状态更新
   */
  @SubscribeMessage('subscribe-system-status')
  handleSubscribeSystemStatus(
    @MessageBody() data: { systemId: string },
    @ConnectedSocket() client: Socket,
  ) {
    this.logger.log(`客户端 ${client.id} 订阅系统状态: ${data.systemId}`);
    
    // 将客户端加入系统状态房间
    client.join(`system-${data.systemId}`);
    
    // 发送当前状态
    this.sendSystemStatus(data.systemId, client.id);
    
    return {
      success: true,
      message: `已订阅系统 ${data.systemId} 的状态更新`,
    };
  }

  /**
   * 订阅流程状态更新
   */
  @SubscribeMessage('subscribe-flow-status')
  handleSubscribeFlowStatus(
    @MessageBody() data: { flowId: string },
    @ConnectedSocket() client: Socket,
  ) {
    this.logger.log(`客户端 ${client.id} 订阅流程状态: ${data.flowId}`);
    
    // 将客户端加入流程状态房间
    client.join(`flow-${data.flowId}`);
    
    return {
      success: true,
      message: `已订阅流程 ${data.flowId} 的状态更新`,
    };
  }

  /**
   * 订阅任务进度更新
   */
  @SubscribeMessage('subscribe-task-progress')
  handleSubscribeTaskProgress(
    @MessageBody() data: { taskId: string },
    @ConnectedSocket() client: Socket,
  ) {
    this.logger.log(`客户端 ${client.id} 订阅任务进度: ${data.taskId}`);
    
    // 将客户端加入任务进度房间
    client.join(`task-${data.taskId}`);
    
    return {
      success: true,
      message: `已订阅任务 ${data.taskId} 的进度更新`,
    };
  }

  /**
   * 取消订阅
   */
  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(
    @MessageBody() data: { type: string; id: string },
    @ConnectedSocket() client: Socket,
  ) {
    const roomName = `${data.type}-${data.id}`;
    client.leave(roomName);
    
    this.logger.log(`客户端 ${client.id} 取消订阅: ${roomName}`);
    
    return {
      success: true,
      message: `已取消订阅 ${roomName}`,
    };
  }

  /**
   * 获取实时统计
   */
  @SubscribeMessage('get-real-time-stats')
  async handleGetRealTimeStats(@ConnectedSocket() client: Socket) {
    try {
      const stats = await this.enterpriseIntegrationService.getIntegrationStatistics();
      
      client.emit('real-time-stats', {
        success: true,
        data: stats,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('获取实时统计失败', error);
      
      client.emit('real-time-stats', {
        success: false,
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  /**
   * 广播系统状态更新
   */
  broadcastSystemStatusUpdate(systemId: string, status: any) {
    this.server.to(`system-${systemId}`).emit('system-status-update', {
      systemId,
      status,
      timestamp: new Date(),
    });
  }

  /**
   * 广播流程状态更新
   */
  broadcastFlowStatusUpdate(flowId: string, status: any) {
    this.server.to(`flow-${flowId}`).emit('flow-status-update', {
      flowId,
      status,
      timestamp: new Date(),
    });
  }

  /**
   * 广播任务进度更新
   */
  broadcastTaskProgressUpdate(taskId: string, progress: any) {
    this.server.to(`task-${taskId}`).emit('task-progress-update', {
      taskId,
      progress,
      timestamp: new Date(),
    });
  }

  /**
   * 广播同步完成事件
   */
  broadcastSyncCompleted(taskId: string, result: any) {
    this.server.emit('sync-completed', {
      taskId,
      result,
      timestamp: new Date(),
    });
  }

  /**
   * 广播同步错误事件
   */
  broadcastSyncError(taskId: string, error: any) {
    this.server.emit('sync-error', {
      taskId,
      error,
      timestamp: new Date(),
    });
  }

  /**
   * 广播系统告警
   */
  broadcastSystemAlert(alert: any) {
    this.server.emit('system-alert', {
      ...alert,
      timestamp: new Date(),
    });
  }

  /**
   * 发送系统状态给特定客户端
   */
  private async sendSystemStatus(systemId: string, clientId?: string) {
    try {
      const status = await this.enterpriseIntegrationService.getSystemStatus(systemId);
      
      const message = {
        systemId,
        status,
        timestamp: new Date(),
      };

      if (clientId) {
        const client = this.connectedClients.get(clientId);
        if (client) {
          client.emit('system-status', message);
        }
      } else {
        this.server.to(`system-${systemId}`).emit('system-status', message);
      }
    } catch (error) {
      this.logger.error(`发送系统状态失败: ${systemId}`, error);
    }
  }

  /**
   * 获取连接的客户端数量
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * 获取所有连接的客户端ID
   */
  getConnectedClientIds(): string[] {
    return Array.from(this.connectedClients.keys());
  }
}
