import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { EnterpriseSystemEntity } from './enterprise-system.entity';
import { IntegrationFlowEntity } from './integration-flow.entity';
import { SyncErrorEntity } from './sync-error.entity';

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  RETRYING = 'retrying',
}

/**
 * 任务类型枚举
 */
export enum TaskType {
  SCHEDULED = 'scheduled',
  MANUAL = 'manual',
  REAL_TIME = 'real_time',
  RETRY = 'retry',
}

/**
 * 数据同步任务实体
 * 记录每次数据同步任务的执行情况
 */
@Entity('sync_tasks')
@Index(['flowId', 'status'])
@Index(['sourceSystemId', 'targetSystemId'])
@Index(['status', 'startTime'])
@Index(['taskType', 'status'])
export class SyncTaskEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true, comment: '任务唯一标识' })
  @Index()
  taskId: string;

  @Column({ type: 'uuid', comment: '集成流程ID' })
  flowId: string;

  @Column({ type: 'uuid', comment: '源系统ID' })
  sourceSystemId: string;

  @Column({ type: 'uuid', comment: '目标系统ID' })
  targetSystemId: string;

  @Column({ type: 'varchar', length: 100, comment: '数据实体' })
  dataEntity: string;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.PENDING,
    comment: '任务状态',
  })
  status: TaskStatus;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.SCHEDULED,
    comment: '任务类型',
  })
  taskType: TaskType;

  // 执行时间
  @Column({ type: 'timestamp', comment: '开始时间' })
  startTime: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '结束时间' })
  endTime: Date;

  @Column({ type: 'int', nullable: true, comment: '执行时长（毫秒）' })
  duration: number;

  // 数据统计
  @Column({ type: 'bigint', default: 0, comment: '待处理记录数' })
  recordsToProcess: number;

  @Column({ type: 'bigint', default: 0, comment: '已处理记录数' })
  recordsProcessed: number;

  @Column({ type: 'bigint', default: 0, comment: '成功处理记录数' })
  recordsSuccessful: number;

  @Column({ type: 'bigint', default: 0, comment: '失败处理记录数' })
  recordsFailed: number;

  @Column({ type: 'bigint', default: 0, comment: '跳过记录数' })
  recordsSkipped: number;

  @Column({ type: 'float', default: 0, comment: '处理进度（百分比）' })
  progress: number;

  // 性能指标
  @Column({ type: 'float', default: 0, comment: '吞吐量（记录/秒）' })
  throughput: number;

  @Column({ type: 'bigint', default: 0, comment: '数据量（字节）' })
  dataVolume: number;

  @Column({ type: 'float', default: 0, comment: '平均记录大小（字节）' })
  averageRecordSize: number;

  // 网络统计
  @Column({ type: 'int', default: 0, comment: '网络请求次数' })
  networkRequests: number;

  @Column({ type: 'float', default: 0, comment: '平均响应时间（毫秒）' })
  averageResponseTime: number;

  @Column({ type: 'bigint', default: 0, comment: '网络传输字节数' })
  networkBytes: number;

  // 错误处理
  @Column({ type: 'int', default: 0, comment: '重试次数' })
  retryCount: number;

  @Column({ type: 'int', default: 3, comment: '最大重试次数' })
  maxRetries: number;

  @Column({ type: 'text', nullable: true, comment: '错误消息' })
  errorMessage: string;

  @Column({ type: 'text', nullable: true, comment: '错误堆栈' })
  errorStack: string;

  // 任务配置
  @Column({ type: 'json', nullable: true, comment: '任务参数' })
  taskParameters: any;

  @Column({ type: 'json', nullable: true, comment: '数据过滤条件' })
  filterConditions: any;

  @Column({ type: 'json', nullable: true, comment: '数据映射规则' })
  dataMappings: any;

  // 检查点信息（用于断点续传）
  @Column({ type: 'json', nullable: true, comment: '检查点数据' })
  checkpointData: any;

  @Column({ type: 'timestamp', nullable: true, comment: '最后检查点时间' })
  lastCheckpointTime: Date;

  // 资源使用情况
  @Column({ type: 'float', default: 0, comment: 'CPU使用率（百分比）' })
  cpuUsage: number;

  @Column({ type: 'bigint', default: 0, comment: '内存使用量（字节）' })
  memoryUsage: number;

  @Column({ type: 'float', default: 0, comment: '磁盘IO（字节/秒）' })
  diskIO: number;

  // 质量指标
  @Column({ type: 'float', default: 0, comment: '数据质量分数（0-100）' })
  dataQualityScore: number;

  @Column({ type: 'int', default: 0, comment: '数据验证错误数' })
  validationErrors: number;

  @Column({ type: 'int', default: 0, comment: '数据转换错误数' })
  transformationErrors: number;

  // 业务指标
  @Column({ type: 'varchar', length: 100, nullable: true, comment: '业务批次号' })
  businessBatchId: string;

  @Column({ type: 'json', nullable: true, comment: '业务标签' })
  businessTags: string[];

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '执行用户' })
  executedBy: string;

  // 审计信息
  @Column({ type: 'json', nullable: true, comment: '审计日志' })
  auditLog: Array<{
    timestamp: Date;
    action: string;
    details: any;
  }>;

  @Column({ type: 'boolean', default: false, comment: '是否包含敏感数据' })
  containsSensitiveData: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '数据分类级别' })
  dataClassification: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => IntegrationFlowEntity, flow => flow.syncTasks)
  @JoinColumn({ name: 'flowId' })
  flow: IntegrationFlowEntity;

  @ManyToOne(() => EnterpriseSystemEntity, system => system.sourceTasks)
  @JoinColumn({ name: 'sourceSystemId' })
  sourceSystem: EnterpriseSystemEntity;

  @ManyToOne(() => EnterpriseSystemEntity, system => system.targetTasks)
  @JoinColumn({ name: 'targetSystemId' })
  targetSystem: EnterpriseSystemEntity;

  @OneToMany(() => SyncErrorEntity, error => error.task)
  errors: SyncErrorEntity[];

  /**
   * 计算成功率
   */
  get successRate(): number {
    if (this.recordsProcessed === 0) return 0;
    return (this.recordsSuccessful / this.recordsProcessed) * 100;
  }

  /**
   * 计算失败率
   */
  get failureRate(): number {
    if (this.recordsProcessed === 0) return 0;
    return (this.recordsFailed / this.recordsProcessed) * 100;
  }

  /**
   * 判断任务是否完成
   */
  get isCompleted(): boolean {
    return [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED].includes(this.status);
  }

  /**
   * 判断任务是否正在运行
   */
  get isRunning(): boolean {
    return [TaskStatus.RUNNING, TaskStatus.RETRYING].includes(this.status);
  }
}
