version: '3.8'

services:
  # 企业集成服务 - 开发模式
  enterprise-integration-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: enterprise-integration-dev
    restart: unless-stopped
    ports:
      - "3020:3020"
      - "9229:9229"  # 调试端口
    environment:
      - NODE_ENV=development
      - PORT=3020
      - DB_HOST=mysql-dev
      - DB_PORT=3306
      - DB_USERNAME=enterprise_user
      - DB_PASSWORD=enterprise_password
      - DB_DATABASE=enterprise_integration_dev
      - DB_SYNCHRONIZE=true
      - DB_LOGGING=true
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
    depends_on:
      mysql-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    networks:
      - enterprise-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    command: npm run start:debug

  # MySQL数据库 - 开发环境
  mysql-dev:
    image: mysql:8.0
    container_name: enterprise-mysql-dev
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=enterprise_integration_dev
      - MYSQL_USER=enterprise_user
      - MYSQL_PASSWORD=enterprise_password
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
    networks:
      - enterprise-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存 - 开发环境
  redis-dev:
    image: redis:7-alpine
    container_name: enterprise-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - enterprise-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # phpMyAdmin - 数据库管理工具
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: enterprise-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql-dev
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=root_password
    depends_on:
      - mysql-dev
    networks:
      - enterprise-dev-network

  # Redis Commander - Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: enterprise-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    depends_on:
      - redis-dev
    networks:
      - enterprise-dev-network

networks:
  enterprise-dev-network:
    driver: bridge

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
