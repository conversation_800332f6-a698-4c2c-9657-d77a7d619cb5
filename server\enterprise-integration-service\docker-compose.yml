version: '3.8'

services:
  # 企业集成服务
  enterprise-integration:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: enterprise-integration-service
    restart: unless-stopped
    ports:
      - "3020:3020"
    environment:
      - NODE_ENV=production
      - PORT=3020
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=enterprise_user
      - DB_PASSWORD=enterprise_password
      - DB_DATABASE=enterprise_integration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - enterprise-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3020/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: enterprise-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=enterprise_integration
      - MYSQL_USER=enterprise_user
      - MYSQL_PASSWORD=enterprise_password
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - enterprise-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: enterprise-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - enterprise-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: enterprise-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - enterprise-integration
    networks:
      - enterprise-network

networks:
  enterprise-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
