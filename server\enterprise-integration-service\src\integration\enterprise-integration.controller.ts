import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { CacheInterceptor } from '@nestjs/cache-manager';

import { EnterpriseIntegrationService } from './enterprise-integration.service';
import {
  RegisterSystemDto,
  CreateFlowDto,
  ExecuteSyncDto,
  RealTimeSyncDto,
} from './dto';
import {
  ApiResponseDto,
  SystemStatusResponseDto,
  IntegrationStatsResponseDto,
  SyncTaskResponseDto,
} from './dto/response.dto';

/**
 * 企业系统深度集成控制器
 * 提供企业系统注册、集成流程管理、数据同步等API接口
 */
@ApiTags('enterprise-integration')
@Controller('enterprise-integration')
@UseGuards(ThrottlerGuard)
@ApiBearerAuth()
export class EnterpriseIntegrationController {
  private readonly logger = new Logger(EnterpriseIntegrationController.name);

  constructor(
    private readonly enterpriseIntegrationService: EnterpriseIntegrationService,
  ) {}

  /**
   * 注册企业系统
   */
  @Post('systems')
  @ApiOperation({
    summary: '注册企业系统',
    description: '注册新的企业系统到集成平台，支持ERP/CRM/SCM/财务/人力资源等系统类型',
  })
  @ApiBody({ type: RegisterSystemDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '系统注册成功',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '系统已存在',
  })
  async registerSystem(@Body() registerSystemDto: RegisterSystemDto): Promise<ApiResponseDto> {
    try {
      this.logger.log(`注册企业系统: ${registerSystemDto.name} (${registerSystemDto.type})`);
      
      const systemId = await this.enterpriseIntegrationService.registerEnterpriseSystem(
        registerSystemDto,
      );

      return {
        success: true,
        message: '企业系统注册成功',
        data: { systemId },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('注册企业系统失败', error);
      throw new HttpException(
        {
          success: false,
          message: error.message || '注册企业系统失败',
          timestamp: new Date(),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 获取企业系统列表
   */
  @Get('systems')
  @ApiOperation({
    summary: '获取企业系统列表',
    description: '获取已注册的企业系统列表，支持分页和筛选',
  })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', example: 10 })
  @ApiQuery({ name: 'type', required: false, description: '系统类型筛选' })
  @ApiQuery({ name: 'status', required: false, description: '状态筛选' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取系统列表成功',
    type: ApiResponseDto,
  })
  @UseInterceptors(CacheInterceptor)
  async getSystemList(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('type') type?: string,
    @Query('status') status?: string,
  ): Promise<ApiResponseDto> {
    try {
      const systems = await this.enterpriseIntegrationService.getSystemList({
        page,
        limit,
        type,
        status,
      });

      return {
        success: true,
        message: '获取企业系统列表成功',
        data: systems,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取企业系统列表失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取企业系统列表失败',
          timestamp: new Date(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取系统状态
   */
  @Get('systems/:systemId/status')
  @ApiOperation({
    summary: '获取系统状态',
    description: '获取指定企业系统的详细状态信息，包括连接状态、性能指标等',
  })
  @ApiParam({ name: 'systemId', description: '系统ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取系统状态成功',
    type: SystemStatusResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '系统不存在',
  })
  async getSystemStatus(@Param('systemId') systemId: string): Promise<SystemStatusResponseDto> {
    try {
      const status = await this.enterpriseIntegrationService.getSystemStatus(systemId);

      return {
        success: true,
        message: '获取系统状态成功',
        data: status,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`获取系统状态失败: ${systemId}`, error);
      throw new HttpException(
        {
          success: false,
          message: error.message || '获取系统状态失败',
          timestamp: new Date(),
        },
        error.message?.includes('不存在') ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 创建集成流程
   */
  @Post('flows')
  @ApiOperation({
    summary: '创建集成流程',
    description: '创建新的数据集成流程，定义数据源、目标系统和转换规则',
  })
  @ApiBody({ type: CreateFlowDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '集成流程创建成功',
    type: ApiResponseDto,
  })
  async createIntegrationFlow(@Body() createFlowDto: CreateFlowDto): Promise<ApiResponseDto> {
    try {
      this.logger.log(`创建集成流程: ${createFlowDto.name}`);
      
      const flowId = await this.enterpriseIntegrationService.createIntegrationFlow(createFlowDto);

      return {
        success: true,
        message: '集成流程创建成功',
        data: { flowId },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('创建集成流程失败', error);
      throw new HttpException(
        {
          success: false,
          message: error.message || '创建集成流程失败',
          timestamp: new Date(),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 执行数据同步
   */
  @Post('sync/execute')
  @ApiOperation({
    summary: '执行数据同步',
    description: '手动执行指定集成流程的数据同步任务',
  })
  @ApiBody({ type: ExecuteSyncDto })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: '同步任务已启动',
    type: SyncTaskResponseDto,
  })
  async executeDataSync(@Body() executeSyncDto: ExecuteSyncDto): Promise<SyncTaskResponseDto> {
    try {
      this.logger.log(`执行数据同步: 流程 ${executeSyncDto.flowId}`);
      
      const taskId = await this.enterpriseIntegrationService.executeDataSync(
        executeSyncDto.flowId,
        true, // 手动执行
      );

      return {
        success: true,
        message: '数据同步任务已启动',
        data: { taskId },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('执行数据同步失败', error);
      throw new HttpException(
        {
          success: false,
          message: error.message || '执行数据同步失败',
          timestamp: new Date(),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 实时数据同步
   */
  @Post('sync/realtime')
  @ApiOperation({
    summary: '实时数据同步',
    description: '执行实时数据同步，立即将数据从源系统同步到目标系统',
  })
  @ApiBody({ type: RealTimeSyncDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '实时同步完成',
    type: ApiResponseDto,
  })
  async realTimeSync(@Body() realTimeSyncDto: RealTimeSyncDto): Promise<ApiResponseDto> {
    try {
      this.logger.log(
        `实时数据同步: ${realTimeSyncDto.sourceSystem} -> ${realTimeSyncDto.targetSystem}`,
      );
      
      const result = await this.enterpriseIntegrationService.realTimeSync(
        realTimeSyncDto.sourceSystem,
        realTimeSyncDto.targetSystem,
        realTimeSyncDto.dataEntity,
        realTimeSyncDto.data,
      );

      return {
        success: true,
        message: '实时数据同步完成',
        data: result,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('实时数据同步失败', error);
      throw new HttpException(
        {
          success: false,
          message: error.message || '实时数据同步失败',
          timestamp: new Date(),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 获取集成统计信息
   */
  @Get('statistics')
  @ApiOperation({
    summary: '获取集成统计信息',
    description: '获取企业系统集成的统计信息，包括系统数量、流程状态、同步任务等',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取统计信息成功',
    type: IntegrationStatsResponseDto,
  })
  @UseInterceptors(CacheInterceptor)
  async getIntegrationStatistics(): Promise<IntegrationStatsResponseDto> {
    try {
      const statistics = await this.enterpriseIntegrationService.getIntegrationStatistics();

      return {
        success: true,
        message: '获取集成统计信息成功',
        data: statistics,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取集成统计信息失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取集成统计信息失败',
          timestamp: new Date(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
