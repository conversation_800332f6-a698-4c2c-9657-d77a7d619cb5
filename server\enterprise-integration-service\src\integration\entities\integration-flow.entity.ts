import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { EnterpriseSystemEntity } from './enterprise-system.entity';
import { SyncTaskEntity } from './sync-task.entity';

/**
 * 数据同步模式枚举
 */
export enum SyncMode {
  REAL_TIME = 'real_time',
  BATCH = 'batch',
  SCHEDULED = 'scheduled',
  EVENT_DRIVEN = 'event_driven',
  HYBRID = 'hybrid',
}

/**
 * 流程状态枚举
 */
export enum FlowStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  ERROR = 'error',
  DISABLED = 'disabled',
}

/**
 * 冲突解决策略枚举
 */
export enum ConflictResolution {
  SOURCE_WINS = 'source_wins',
  TARGET_WINS = 'target_wins',
  MANUAL = 'manual',
  MERGE = 'merge',
}

/**
 * 转换步骤类型枚举
 */
export enum TransformationType {
  MAPPING = 'mapping',
  VALIDATION = 'validation',
  ENRICHMENT = 'enrichment',
  FILTERING = 'filtering',
  AGGREGATION = 'aggregation',
}

/**
 * 集成流程实体
 * 定义数据集成的流程配置和规则
 */
@Entity('integration_flows')
@Index(['sourceSystemId', 'status'])
@Index(['targetSystemId', 'status'])
@Index(['status', 'nextExecution'])
export class IntegrationFlowEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true, comment: '流程唯一标识' })
  @Index()
  flowId: string;

  @Column({ type: 'varchar', length: 200, comment: '流程名称' })
  name: string;

  @Column({ type: 'text', nullable: true, comment: '流程描述' })
  description: string;

  @Column({ type: 'uuid', comment: '源系统ID' })
  sourceSystemId: string;

  @Column({ type: 'uuid', comment: '目标系统ID' })
  targetSystemId: string;

  @Column({ type: 'json', comment: '数据实体列表' })
  dataEntities: string[];

  @Column({
    type: 'enum',
    enum: FlowStatus,
    default: FlowStatus.ACTIVE,
    comment: '流程状态',
  })
  status: FlowStatus;

  // 同步配置
  @Column({
    type: 'enum',
    enum: SyncMode,
    default: SyncMode.BATCH,
    comment: '同步模式',
  })
  syncMode: SyncMode;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '调度表达式（Cron）' })
  schedule: string;

  @Column({ type: 'int', default: 1000, comment: '批处理大小' })
  batchSize: number;

  @Column({
    type: 'enum',
    enum: ConflictResolution,
    default: ConflictResolution.SOURCE_WINS,
    comment: '冲突解决策略',
  })
  conflictResolution: ConflictResolution;

  @Column({ type: 'boolean', default: true, comment: '是否启用增量同步' })
  deltaSync: boolean;

  @Column({ type: 'boolean', default: true, comment: '是否启用压缩' })
  compressionEnabled: boolean;

  // 数据映射配置
  @Column({ type: 'json', nullable: true, comment: '数据映射规则' })
  dataMappings: Array<{
    sourceField: string;
    targetField: string;
    transformation?: string;
    validation?: string;
    required: boolean;
    defaultValue?: any;
  }>;

  // 转换步骤
  @Column({ type: 'json', nullable: true, comment: '数据转换步骤' })
  transformationSteps: Array<{
    stepId: string;
    type: TransformationType;
    configuration: any;
    order: number;
  }>;

  // 过滤条件
  @Column({ type: 'json', nullable: true, comment: '数据过滤条件' })
  filterConditions: any;

  // 错误处理配置
  @Column({ type: 'int', default: 3, comment: '最大重试次数' })
  maxRetries: number;

  @Column({ type: 'varchar', length: 50, default: 'exponential', comment: '退避策略' })
  backoffStrategy: string;

  @Column({ type: 'int', default: 1000, comment: '基础延迟时间（毫秒）' })
  baseDelay: number;

  @Column({ type: 'boolean', default: true, comment: '是否启用死信队列' })
  deadLetterQueue: boolean;

  // 告警配置
  @Column({ type: 'boolean', default: true, comment: '是否启用告警' })
  alertingEnabled: boolean;

  @Column({ type: 'json', nullable: true, comment: '告警渠道' })
  alertChannels: string[];

  @Column({ type: 'int', default: 5, comment: '告警阈值' })
  alertThreshold: number;

  // 执行时间
  @Column({ type: 'timestamp', nullable: true, comment: '最后执行时间' })
  lastExecution: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '下次执行时间' })
  nextExecution: Date;

  // 统计信息
  @Column({ type: 'int', default: 0, comment: '总执行次数' })
  totalExecutions: number;

  @Column({ type: 'int', default: 0, comment: '成功执行次数' })
  successfulExecutions: number;

  @Column({ type: 'int', default: 0, comment: '失败执行次数' })
  failedExecutions: number;

  @Column({ type: 'float', default: 0, comment: '平均执行时间（毫秒）' })
  averageExecutionTime: number;

  @Column({ type: 'float', default: 0, comment: '最后执行时间（毫秒）' })
  lastExecutionTime: number;

  @Column({ type: 'bigint', default: 0, comment: '已处理记录数' })
  recordsProcessed: number;

  @Column({ type: 'int', default: 0, comment: '遇到的错误数' })
  errorsEncountered: number;

  // 性能指标
  @Column({ type: 'float', default: 0, comment: '平均吞吐量（记录/秒）' })
  averageThroughput: number;

  @Column({ type: 'float', default: 0, comment: '峰值吞吐量（记录/秒）' })
  peakThroughput: number;

  @Column({ type: 'bigint', default: 0, comment: '总数据量（字节）' })
  totalDataVolume: number;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => EnterpriseSystemEntity, system => system.sourceFlows)
  @JoinColumn({ name: 'sourceSystemId' })
  sourceSystem: EnterpriseSystemEntity;

  @ManyToOne(() => EnterpriseSystemEntity, system => system.targetFlows)
  @JoinColumn({ name: 'targetSystemId' })
  targetSystem: EnterpriseSystemEntity;

  @OneToMany(() => SyncTaskEntity, task => task.flow)
  syncTasks: SyncTaskEntity[];
}
