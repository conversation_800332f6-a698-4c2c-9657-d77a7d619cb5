import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
} from 'typeorm';
import { IntegrationFlowEntity } from './integration-flow.entity';
import { SyncTaskEntity } from './sync-task.entity';

/**
 * 企业系统类型枚举
 */
export enum EnterpriseSystemType {
  ERP = 'erp',
  CRM = 'crm',
  SCM = 'scm',
  FINANCE = 'finance',
  HR = 'hr',
  PLM = 'plm',
  WMS = 'wms',
  TMS = 'tms',
  QMS = 'qms',
  BPM = 'bpm',
  BI = 'bi',
  OA = 'oa',
}

/**
 * 集成协议枚举
 */
export enum IntegrationProtocol {
  REST_API = 'rest_api',
  SOAP = 'soap',
  GRAPHQL = 'graphql',
  RFC = 'rfc',
  ODBC = 'odbc',
  JDBC = 'jdbc',
  FTP = 'ftp',
  SFTP = 'sftp',
  EDI = 'edi',
  KAFKA = 'kafka',
  RABBITMQ = 'rabbitmq',
  WEBSOCKET = 'websocket',
}

/**
 * 系统状态枚举
 */
export enum SystemStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
  ERROR = 'error',
}

/**
 * 认证类型枚举
 */
export enum AuthenticationType {
  BASIC = 'basic',
  OAUTH2 = 'oauth2',
  API_KEY = 'api_key',
  CERTIFICATE = 'certificate',
  SAML = 'saml',
  LDAP = 'ldap',
}

/**
 * 企业系统实体
 * 存储企业系统的基本信息和配置
 */
@Entity('enterprise_systems')
@Index(['type', 'status'])
@Index(['vendor', 'version'])
export class EnterpriseSystemEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true, comment: '系统唯一标识' })
  @Index()
  systemId: string;

  @Column({ type: 'varchar', length: 200, comment: '系统名称' })
  name: string;

  @Column({
    type: 'enum',
    enum: EnterpriseSystemType,
    comment: '系统类型',
  })
  type: EnterpriseSystemType;

  @Column({ type: 'varchar', length: 100, comment: '系统厂商' })
  vendor: string;

  @Column({ type: 'varchar', length: 50, comment: '系统版本' })
  version: string;

  @Column({ type: 'varchar', length: 500, comment: '系统端点地址' })
  endpoint: string;

  @Column({
    type: 'enum',
    enum: IntegrationProtocol,
    comment: '集成协议',
  })
  protocol: IntegrationProtocol;

  @Column({
    type: 'enum',
    enum: SystemStatus,
    default: SystemStatus.INACTIVE,
    comment: '系统状态',
  })
  status: SystemStatus;

  @Column({ type: 'timestamp', nullable: true, comment: '最后同步时间' })
  lastSync: Date;

  @Column({ type: 'text', nullable: true, comment: '系统描述' })
  description: string;

  // 认证配置
  @Column({
    type: 'enum',
    enum: AuthenticationType,
    comment: '认证类型',
  })
  authenticationType: AuthenticationType;

  @Column({ type: 'json', nullable: true, comment: '认证凭据（加密存储）' })
  authenticationCredentials: any;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '令牌端点' })
  tokenEndpoint: string;

  @Column({ type: 'timestamp', nullable: true, comment: '令牌过期时间' })
  tokenExpiresAt: Date;

  // 连接配置
  @Column({ type: 'int', default: 10, comment: '最大连接数' })
  maxConnections: number;

  @Column({ type: 'int', default: 30000, comment: '连接超时时间（毫秒）' })
  connectionTimeout: number;

  @Column({ type: 'int', default: 3, comment: '重试次数' })
  retryAttempts: number;

  // 系统能力
  @Column({ type: 'json', nullable: true, comment: '支持的操作' })
  supportedOperations: string[];

  @Column({ type: 'json', nullable: true, comment: '支持的数据格式' })
  dataFormats: string[];

  @Column({ type: 'int', default: 1000, comment: '单次请求最大记录数' })
  maxRecordsPerRequest: number;

  @Column({ type: 'int', default: 10, comment: '每秒请求限制' })
  requestsPerSecond: number;

  @Column({ type: 'int', default: 1000, comment: '每小时请求限制' })
  requestsPerHour: number;

  @Column({ type: 'boolean', default: true, comment: '是否支持批量操作' })
  bulkOperations: boolean;

  @Column({ type: 'boolean', default: false, comment: '是否支持Webhook' })
  webhookSupport: boolean;

  // 安全配置
  @Column({ type: 'boolean', default: true, comment: '是否启用加密' })
  encryptionEnabled: boolean;

  @Column({ type: 'varchar', length: 50, default: 'AES-256', comment: '加密算法' })
  encryptionAlgorithm: string;

  @Column({ type: 'boolean', default: true, comment: '是否启用密钥轮换' })
  keyRotation: boolean;

  @Column({ type: 'json', nullable: true, comment: 'PII字段列表' })
  piiFields: string[];

  @Column({ type: 'boolean', default: true, comment: '是否启用数据匿名化' })
  anonymization: boolean;

  @Column({ type: 'int', default: 365, comment: '数据保留天数' })
  dataRetentionDays: number;

  @Column({ type: 'boolean', default: true, comment: '是否启用审计日志' })
  auditLogging: boolean;

  // 监控统计
  @Column({ type: 'int', default: 0, comment: '总请求次数' })
  totalRequests: number;

  @Column({ type: 'int', default: 0, comment: '成功请求次数' })
  successfulRequests: number;

  @Column({ type: 'int', default: 0, comment: '失败请求次数' })
  failedRequests: number;

  @Column({ type: 'float', default: 0, comment: '平均响应时间（毫秒）' })
  averageResponseTime: number;

  @Column({ type: 'timestamp', nullable: true, comment: '最后健康检查时间' })
  lastHealthCheck: Date;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => IntegrationFlowEntity, flow => flow.sourceSystem)
  sourceFlows: IntegrationFlowEntity[];

  @OneToMany(() => IntegrationFlowEntity, flow => flow.targetSystem)
  targetFlows: IntegrationFlowEntity[];

  @OneToMany(() => SyncTaskEntity, task => task.sourceSystem)
  sourceTasks: SyncTaskEntity[];

  @OneToMany(() => SyncTaskEntity, task => task.targetSystem)
  targetTasks: SyncTaskEntity[];
}
