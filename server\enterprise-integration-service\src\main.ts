import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';

/**
 * 企业系统深度集成服务启动入口
 * 提供ERP/CRM/SCM/财务/人力资源系统无缝集成
 */
async function bootstrap() {
  const logger = new Logger('EnterpriseIntegrationService');
  
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      credentials: true,
    });

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        disableErrorMessages: false,
        validationError: {
          target: false,
          value: false,
        },
      }),
    );

    // Swagger API文档配置
    const config = new DocumentBuilder()
      .setTitle('企业系统深度集成服务')
      .setDescription('提供ERP/CRM/SCM/财务/人力资源系统无缝集成的微服务API')
      .setVersion('1.0.0')
      .addTag('enterprise-integration', '企业系统集成管理')
      .addTag('system-registry', '企业系统注册')
      .addTag('integration-flows', '集成流程管理')
      .addTag('data-sync', '数据同步')
      .addTag('monitoring', '监控统计')
      .addTag('health', '健康检查')
      .addBearerAuth()
      .addServer('http://localhost:3020', '开发环境')
      .addServer('https://api.enterprise.com', '生产环境')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'none',
        filter: true,
        showRequestHeaders: true,
      },
    });

    // 微服务配置（可选）
    try {
      app.connectMicroservice<MicroserviceOptions>({
        transport: Transport.REDIS,
        options: {
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD'),
          retryAttempts: 3,
          retryDelay: 1000,
        },
      });

      // 启动微服务
      await app.startAllMicroservices();
      logger.log(`📡 微服务: Redis Transport 已连接`);
    } catch (error: any) {
      logger.warn(`⚠️  微服务连接失败，将以HTTP模式运行: ${error.message}`);
    }

    // 启动HTTP服务器
    const port = configService.get<number>('PORT', 3020);
    const host = configService.get<string>('HOST', '0.0.0.0');
    
    await app.listen(port, host);

    logger.log(`🚀 企业系统深度集成服务已启动`);
    logger.log(`📖 API文档: http://${host}:${port}/api/docs`);
    logger.log(`🔗 服务地址: http://${host}:${port}/api/v1`);
    logger.log(`🌐 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('启动企业系统深度集成服务失败:', error);
    process.exit(1);
  }
}

bootstrap();
