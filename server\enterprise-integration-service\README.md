# 企业系统深度集成服务 (Enterprise Integration Service)

## 📋 项目概述

企业系统深度集成服务是一个基于NestJS的微服务，专门用于企业级系统间的数据集成和同步。支持ERP、CRM、SCM、财务、人力资源等多种企业系统的无缝集成，提供实时数据同步、批量数据处理、智能冲突解决等核心功能。

## 🚀 核心功能

### 🔗 企业系统集成
- **多系统支持**: ERP、CRM、SCM、财务、HR、PLM、WMS、TMS、QMS、BPM、BI、OA
- **多协议支持**: REST API、SOAP、GraphQL、RFC、ODBC、JDBC、FTP、SFTP、EDI、Kafka、RabbitMQ、WebSocket
- **智能连接管理**: 连接池、超时控制、重试机制、健康检查

### 📊 数据同步
- **实时同步**: 基于事件驱动的实时数据同步
- **批量同步**: 高效的批量数据处理
- **增量同步**: 智能识别数据变更，减少传输量
- **冲突解决**: 多种冲突解决策略（源优先、目标优先、手动、合并）

### 🔄 集成流程管理
- **可视化配置**: 通过API配置数据映射和转换规则
- **流程调度**: 支持Cron表达式的定时任务
- **错误处理**: 完善的错误处理和重试机制
- **监控告警**: 实时监控和多渠道告警

### 🛡️ 安全与合规
- **数据加密**: AES-256加密算法
- **身份认证**: 支持多种认证方式（Basic、OAuth2、API Key、证书、SAML、LDAP）
- **数据隐私**: PII字段识别和匿名化
- **审计日志**: 完整的操作审计记录

## 🏗️ 技术架构

### 技术栈
- **框架**: NestJS 9.x
- **语言**: TypeScript 5.x
- **数据库**: MySQL 8.0+ / TypeORM
- **缓存**: Redis 6.0+
- **消息队列**: Bull Queue (Redis)
- **API文档**: Swagger/OpenAPI 3.0
- **监控**: Terminus健康检查

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ERP System    │    │   CRM System    │    │   SCM System    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │  Enterprise Integration   │
                    │       Service            │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴───────┐    ┌─────────┴───────┐    ┌─────────┴───────┐
    │   Finance   │    │       HR        │    │      PLM        │
    │   System    │    │     System      │    │     System      │
    └─────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js 18.x+
- MySQL 8.0+
- Redis 6.0+
- npm 8.x+ 或 yarn 1.22+

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 环境配置
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE enterprise_integration CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
npm run migration:run
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 验证服务
```bash
# 健康检查
curl http://localhost:3020/health

# API文档
open http://localhost:3020/api/docs
```

## 📖 API文档

### 核心接口

#### 1. 注册企业系统
```http
POST /api/v1/enterprise-integration/systems
Content-Type: application/json

{
  "name": "SAP ERP系统",
  "type": "erp",
  "vendor": "SAP",
  "version": "S/4HANA 2021",
  "endpoint": "https://erp.company.com/api/v1",
  "protocol": "rest_api",
  "authentication": {
    "type": "api_key",
    "credentials": {
      "apiKey": "your-api-key"
    }
  }
}
```

#### 2. 创建集成流程
```http
POST /api/v1/enterprise-integration/flows
Content-Type: application/json

{
  "name": "ERP到CRM客户数据同步",
  "sourceSystem": "erp-system-001",
  "targetSystem": "crm-system-001",
  "dataEntities": ["customers"],
  "syncMode": "real_time",
  "dataMappings": [
    {
      "sourceField": "customer_name",
      "targetField": "customerName",
      "required": true
    }
  ]
}
```

#### 3. 执行数据同步
```http
POST /api/v1/enterprise-integration/sync/execute
Content-Type: application/json

{
  "flowId": "flow-001",
  "forceFullSync": false
}
```

### WebSocket事件

#### 连接
```javascript
const socket = io('http://localhost:3020/enterprise-integration');

// 订阅系统状态
socket.emit('subscribe-system-status', { systemId: 'erp-system-001' });

// 监听状态更新
socket.on('system-status-update', (data) => {
  console.log('系统状态更新:', data);
});
```

## 🔧 配置说明

### 数据库配置
```env
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=enterprise_user
DB_PASSWORD=enterprise_password
DB_DATABASE=enterprise_integration
```

### Redis配置
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### 安全配置
```env
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key
ENCRYPTION_ALGORITHM=AES-256-GCM
```

## 🐳 Docker部署

### 构建镜像
```bash
docker build -t enterprise-integration-service .
```

### 运行容器
```bash
docker run -d \
  --name enterprise-integration \
  -p 3020:3020 \
  -e DB_HOST=mysql \
  -e REDIS_HOST=redis \
  enterprise-integration-service
```

### Docker Compose
```bash
docker-compose up -d
```

## 📊 监控与运维

### 健康检查
- **基础检查**: `GET /health`
- **详细检查**: `GET /health/detailed`
- **就绪检查**: `GET /health/ready`
- **存活检查**: `GET /health/live`
- **性能指标**: `GET /health/metrics`

### 日志管理
```bash
# 查看实时日志
docker logs -f enterprise-integration

# 查看错误日志
grep "ERROR" logs/enterprise-integration.log
```

### 性能监控
- CPU使用率监控
- 内存使用监控
- 数据库连接池监控
- Redis连接监控
- 同步任务性能监控

## 🧪 测试

### 单元测试
```bash
npm run test
```

### 集成测试
```bash
npm run test:e2e
```

### 测试覆盖率
```bash
npm run test:cov
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与联系

- 📧 邮箱: <EMAIL>
- 📖 文档: https://docs.enterprise-integration.com
- 🐛 问题反馈: https://github.com/company/enterprise-integration-service/issues

## 🔄 更新日志

### v1.0.0 (2023-12-01)
- ✨ 初始版本发布
- 🔗 支持多种企业系统集成
- 📊 实时数据同步功能
- 🛡️ 完善的安全机制
- 📈 监控和告警功能
