import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsObject,
  IsArray,
  Length,
} from 'class-validator';

/**
 * 实时数据同步DTO
 */
export class RealTimeSyncDto {
  @ApiProperty({
    description: '源系统ID',
    example: 'erp-system-001',
  })
  @IsString()
  @Length(1, 100)
  sourceSystem: string;

  @ApiProperty({
    description: '目标系统ID',
    example: 'crm-system-001',
  })
  @IsString()
  @Length(1, 100)
  targetSystem: string;

  @ApiProperty({
    description: '数据实体',
    example: 'customers',
  })
  @IsString()
  @Length(1, 100)
  dataEntity: string;

  @ApiProperty({
    description: '同步数据',
    example: {
      id: '12345',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890'
    },
  })
  @IsObject()
  data: any;

  @ApiPropertyOptional({
    description: '操作类型',
    example: 'create',
    enum: ['create', 'update', 'delete'],
  })
  @IsOptional()
  @IsString()
  operation?: string;

  @ApiPropertyOptional({
    description: '业务标签',
    example: ['real-time', 'customer-update'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  businessTags?: string[];

  @ApiPropertyOptional({
    description: '优先级',
    example: 'high',
    enum: ['low', 'medium', 'high', 'critical'],
  })
  @IsOptional()
  @IsString()
  priority?: string;
}
