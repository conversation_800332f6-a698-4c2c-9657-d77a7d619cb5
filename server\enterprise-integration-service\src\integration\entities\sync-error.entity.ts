import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { SyncTaskEntity } from './sync-task.entity';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  CONNECTION_ERROR = 'connection_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  AUTHORIZATION_ERROR = 'authorization_error',
  VALIDATION_ERROR = 'validation_error',
  TRANSFORMATION_ERROR = 'transformation_error',
  MAPPING_ERROR = 'mapping_error',
  BUSINESS_RULE_ERROR = 'business_rule_error',
  DATA_FORMAT_ERROR = 'data_format_error',
  TIMEOUT_ERROR = 'timeout_error',
  RATE_LIMIT_ERROR = 'rate_limit_error',
  SYSTEM_ERROR = 'system_error',
  UNKNOWN_ERROR = 'unknown_error',
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * 错误状态枚举
 */
export enum ErrorStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  IGNORED = 'ignored',
}

/**
 * 同步错误实体
 * 记录数据同步过程中发生的错误详情
 */
@Entity('sync_errors')
@Index(['taskId', 'errorType'])
@Index(['errorType', 'severity'])
@Index(['status', 'timestamp'])
@Index(['recordId'])
export class SyncErrorEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true, comment: '错误唯一标识' })
  @Index()
  errorId: string;

  @Column({ type: 'uuid', comment: '同步任务ID' })
  taskId: string;

  @Column({ type: 'varchar', length: 200, nullable: true, comment: '记录ID' })
  recordId: string;

  @Column({
    type: 'enum',
    enum: ErrorType,
    comment: '错误类型',
  })
  errorType: ErrorType;

  @Column({
    type: 'enum',
    enum: ErrorSeverity,
    default: ErrorSeverity.MEDIUM,
    comment: '错误严重程度',
  })
  severity: ErrorSeverity;

  @Column({
    type: 'enum',
    enum: ErrorStatus,
    default: ErrorStatus.OPEN,
    comment: '错误状态',
  })
  status: ErrorStatus;

  @Column({ type: 'varchar', length: 500, comment: '错误消息' })
  errorMessage: string;

  @Column({ type: 'text', nullable: true, comment: '错误详细描述' })
  errorDescription: string;

  @Column({ type: 'text', nullable: true, comment: '错误堆栈信息' })
  stackTrace: string;

  @Column({ type: 'varchar', length: 200, nullable: true, comment: '错误代码' })
  errorCode: string;

  // 上下文信息
  @Column({ type: 'varchar', length: 100, nullable: true, comment: '源系统' })
  sourceSystem: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '目标系统' })
  targetSystem: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '数据实体' })
  dataEntity: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '字段名称' })
  fieldName: string;

  @Column({ type: 'text', nullable: true, comment: '字段值' })
  fieldValue: string;

  // 错误数据
  @Column({ type: 'json', nullable: true, comment: '原始数据' })
  originalData: any;

  @Column({ type: 'json', nullable: true, comment: '转换后数据' })
  transformedData: any;

  @Column({ type: 'json', nullable: true, comment: '验证规则' })
  validationRules: any;

  @Column({ type: 'json', nullable: true, comment: '映射规则' })
  mappingRules: any;

  // 网络信息
  @Column({ type: 'varchar', length: 500, nullable: true, comment: '请求URL' })
  requestUrl: string;

  @Column({ type: 'varchar', length: 20, nullable: true, comment: 'HTTP方法' })
  httpMethod: string;

  @Column({ type: 'int', nullable: true, comment: 'HTTP状态码' })
  httpStatusCode: number;

  @Column({ type: 'json', nullable: true, comment: '请求头' })
  requestHeaders: any;

  @Column({ type: 'json', nullable: true, comment: '响应头' })
  responseHeaders: any;

  @Column({ type: 'text', nullable: true, comment: '请求体' })
  requestBody: string;

  @Column({ type: 'text', nullable: true, comment: '响应体' })
  responseBody: string;

  // 重试信息
  @Column({ type: 'int', default: 0, comment: '重试次数' })
  retryCount: number;

  @Column({ type: 'int', default: 3, comment: '最大重试次数' })
  maxRetries: number;

  @Column({ type: 'timestamp', nullable: true, comment: '下次重试时间' })
  nextRetryTime: Date;

  @Column({ type: 'boolean', default: false, comment: '是否可重试' })
  retryable: boolean;

  // 解决信息
  @Column({ type: 'text', nullable: true, comment: '解决方案' })
  resolution: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '解决人员' })
  resolvedBy: string;

  @Column({ type: 'timestamp', nullable: true, comment: '解决时间' })
  resolvedAt: Date;

  @Column({ type: 'text', nullable: true, comment: '解决备注' })
  resolutionNotes: string;

  // 影响分析
  @Column({ type: 'int', default: 1, comment: '影响记录数' })
  affectedRecords: number;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '业务影响' })
  businessImpact: string;

  @Column({ type: 'boolean', default: false, comment: '是否阻塞流程' })
  blockingFlow: boolean;

  // 分类标签
  @Column({ type: 'json', nullable: true, comment: '错误标签' })
  tags: string[];

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '错误分类' })
  category: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '错误子分类' })
  subcategory: string;

  // 监控信息
  @Column({ type: 'boolean', default: false, comment: '是否已发送告警' })
  alertSent: boolean;

  @Column({ type: 'timestamp', nullable: true, comment: '告警发送时间' })
  alertSentAt: Date;

  @Column({ type: 'json', nullable: true, comment: '告警接收人' })
  alertRecipients: string[];

  // 统计信息
  @Column({ type: 'int', default: 1, comment: '发生次数' })
  occurrenceCount: number;

  @Column({ type: 'timestamp', nullable: true, comment: '首次发生时间' })
  firstOccurrence: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '最后发生时间' })
  lastOccurrence: Date;

  @Column({ type: 'timestamp', comment: '错误发生时间' })
  timestamp: Date;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => SyncTaskEntity, task => task.errors)
  @JoinColumn({ name: 'taskId' })
  task: SyncTaskEntity;

  /**
   * 判断错误是否已解决
   */
  get isResolved(): boolean {
    return this.status === ErrorStatus.RESOLVED;
  }

  /**
   * 判断错误是否为关键错误
   */
  get isCritical(): boolean {
    return this.severity === ErrorSeverity.CRITICAL;
  }

  /**
   * 判断是否需要立即处理
   */
  get requiresImmediateAttention(): boolean {
    return this.severity === ErrorSeverity.CRITICAL || this.blockingFlow;
  }

  /**
   * 获取错误持续时间（分钟）
   */
  get durationMinutes(): number {
    if (!this.resolvedAt) {
      return Math.floor((new Date().getTime() - this.timestamp.getTime()) / (1000 * 60));
    }
    return Math.floor((this.resolvedAt.getTime() - this.timestamp.getTime()) / (1000 * 60));
  }
}
