{"name": "emotion-service", "version": "1.0.0", "description": "情感分析服务 - 提供文本情感分析、情感数据存储和情感模式分析功能", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d src/config/database.config.ts", "migration:run": "npm run typeorm -- migration:run -d src/config/database.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/config/database.config.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/schedule": "^4.0.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.7", "ioredis": "^5.6.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "moment": "^2.29.4", "lodash": "^4.17.21", "@node-rs/jieba": "^2.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/lodash": "^4.14.195", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "keywords": ["emotion", "sentiment", "analysis", "nlp", "<PERSON><PERSON><PERSON>", "microservice", "chinese", "text-analysis"], "author": "DL Engine Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/emotion-service.git"}, "bugs": {"url": "https://github.com/your-org/emotion-service/issues"}, "homepage": "https://github.com/your-org/emotion-service#readme"}