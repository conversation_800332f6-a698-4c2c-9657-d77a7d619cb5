@echo off
chcp 65001 >nul
echo 🔧 修复情感分析服务依赖问题...

REM 检查 Node.js 是否安装
echo 📋 检查 Node.js 版本...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js >= 18.0.0
    pause
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

REM 清理旧的依赖
echo 🧹 清理旧的依赖...
if exist node_modules (
    echo 删除 node_modules 目录...
    rmdir /s /q node_modules
)

if exist package-lock.json (
    echo 删除 package-lock.json 文件...
    del package-lock.json
)

REM 清理 npm 缓存
echo 🗑️  清理 npm 缓存...
npm cache clean --force

REM 安装依赖
echo 📦 安装项目依赖...
npm install

if %errorlevel% equ 0 (
    echo ✅ 依赖安装成功！
    
    REM 验证关键依赖
    echo 🔍 验证关键依赖...
    
    npm list @node-rs/jieba >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ @node-rs/jieba 安装成功
    ) else (
        echo ❌ @node-rs/jieba 安装失败
    )
    
    npm list ioredis >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ ioredis 安装成功
    ) else (
        echo ❌ ioredis 安装失败
    )
    
    npm list @nestjs/core >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ @nestjs/core 安装成功
    ) else (
        echo ❌ @nestjs/core 安装失败
    )
    
    echo.
    echo 🎉 依赖修复完成！
    echo.
    echo 📝 下一步操作：
    echo 1. 配置环境变量: copy .env.example .env
    echo 2. 编辑 .env 文件配置数据库连接
    echo 3. 启动服务: npm run start:dev
    echo.
    
) else (
    echo ❌ 依赖安装失败！
    echo.
    echo 🔍 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 尝试使用不同的 npm 镜像源：
    echo    npm config set registry https://registry.npmmirror.com/
    echo 3. 如果在中国，可以使用淘宝镜像：
    echo    npm install -g cnpm --registry=https://registry.npmmirror.com
    echo    cnpm install
    echo 4. 以管理员身份运行此脚本
    echo.
)

pause
