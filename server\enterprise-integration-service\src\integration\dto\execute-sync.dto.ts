import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsObject,
  IsArray,
  IsBoolean,
  Length,
} from 'class-validator';

/**
 * 执行数据同步DTO
 */
export class ExecuteSyncDto {
  @ApiProperty({
    description: '集成流程ID',
    example: 'flow-001',
  })
  @IsString()
  @Length(1, 100)
  flowId: string;

  @ApiPropertyOptional({
    description: '任务参数',
    example: { priority: 'high', timeout: 300000 },
  })
  @IsOptional()
  @IsObject()
  taskParameters?: any;

  @ApiPropertyOptional({
    description: '数据过滤条件',
    example: { status: 'active', updated_date: { $gte: '2023-01-01' } },
  })
  @IsOptional()
  @IsObject()
  filterConditions?: any;

  @ApiPropertyOptional({
    description: '是否强制全量同步',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  forceFullSync?: boolean;

  @ApiPropertyOptional({
    description: '业务批次号',
    example: 'BATCH-************',
  })
  @IsOptional()
  @IsString()
  businessBatchId?: string;

  @ApiPropertyOptional({
    description: '业务标签',
    example: ['urgent', 'customer-data'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  businessTags?: string[];
}
