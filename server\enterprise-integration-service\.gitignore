# 编译输出
/dist
/build
*.tsbuildinfo

# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc测试覆盖率
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1声明文件
typings/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn v2的输出
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 日志文件
logs
*.log

# 数据库
*.sqlite
*.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 测试
/coverage
/test-results

# 备份文件
*.bak
*.backup

# 证书文件
*.pem
*.key
*.crt
*.p12

# 配置文件
config/local.json
config/production.json

# 上传文件
uploads/
public/uploads/

# 缓存
.cache/
.tmp/
